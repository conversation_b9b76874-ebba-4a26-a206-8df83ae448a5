#include "senderMMSProcessDB.h"
#include "Encrypt.h"
#include "ksbase64.h"
#include <sys/time.h>
#include <iconv.h>
//#include <time.h>

// External declarations for database charset global variables
extern char g_db_charset[50];
extern bool g_db_is_euckr;
extern bool g_charset_checked;

static CMMSPacketSend mmsPacketSend;
static int S_PROCESS_NO;

// UTF-8 to EUC-KR conversion function
int utf8ToEuckr(const char *source, char *dest, int dest_size)
{
    iconv_t it;
    char *pout;
    size_t in_size, out_size;
    char *psource = (char*)source; // iconv requires non-const char*

    it = iconv_open("EUC-KR", "UTF-8");
    if (it == (iconv_t)-1) {
        return -1;
    }

    in_size = strlen(source);
    out_size = dest_size - 1; // reserve space for null terminator
    pout = dest;

    if (iconv(it, &psource, &in_size, &pout, &out_size) == (size_t)-1) {
        iconv_close(it);
        return -1;
    }

    *pout = '\0'; // add null terminator
    iconv_close(it);
    return (pout - dest);
}

// 20170621 MMSID SEQ USE
static int proc_id;

int main(int argc,char* argv[])
{
	
	/*
	 * 1 : sockfd
	 * 2 : pipe
	 * 3 : version
	 * 4 : conf file
	 */
	int sockfd;
	int fd;
	int ret;
	char buff[SOCKET_BUFF];
	CLogonDbInfo logonDbInfo;
	
	sockfd = atoi(argv[1]);
	fd = atoi(argv[2]);
	memset(&logonDbInfo,0x00,sizeof(logonDbInfo));
	read(fd,(char*)&logonDbInfo,sizeof(logonDbInfo));
	close(fd);
	
	memset(_DATALOG,0x00,sizeof(_DATALOG));//CCL(_DATALOG);
	memset(_MONILOG,0x00,sizeof(_MONILOG));//CCL(_MONILOG);
	char* p;
	
	sprintf(_DATALOG,"%s/",logonDbInfo.szLogPath);
	sprintf(_MONILOG,"%s/",logonDbInfo.szLogPath);
	
	memset(szSenderID,0x00,sizeof(szSenderID));//CCL(szSenderID);
	strcpy(szSenderID,logonDbInfo.szCID);
	S_PROCESS_NO = getpid();
	
	p = strtok(logonDbInfo.szLogFilePath,"|");
	if( p )
	{
		strcat(_MONILOG,p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get monitor [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	p = strtok(NULL,"|");
	if( p )
	{
		strcat(_DATALOG,p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get data [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	logPrintS(0,"[INF] filepath - logfile[%s] monitorfile[%s] PID[%d]",_DATALOG,_MONILOG, S_PROCESS_NO);
	logPrintS(0,"[INF] argv[4][%s]",argv[4]);
		
	ret = configParse(argv[4]);
	
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] configParse Failed");
	    exit(1);
	}
	
	logPrintS(0,"[INF] config file - logonDBName [%s]",gConf.logonDBName);
	
	if (g_oracle.connectToOracle(gConf.dbuid, gConf.dbdsn)<0)
	{
		logPrintS(0,"[ERR] connectToOracle Failed");
	    return -1;
	}
	
	logPrintS(1,"[INF] ORACLE CONNECT");

	// 20170621 MMSID SEQ USE
	proc_id = g_oracle.selectSEQ();
	
	if( proc_id == -1 )
		proc_id = 9999;	
	
	SenderProcess *mSenderProcess = new SenderProcess();

	// sendre Main Process Start 
	mSenderProcess->SenderMain(sockfd,logonDbInfo);
	
	if (g_oracle.closeFromOracle()<0)
	{
		logPrintS(0,"[ERR] closeFromOracle Failed");
	    return -1;
	}
	logPrintS(1,"[INF] ORACLE DISCONNECT");
	
	return 0;
}


int sendAck(CKSSocket& hRemoteSock,CMMSPacketSend& mmsPacketSend, int nCode,int ctnid,string strDesc)
{
	int ret=0;
	string strPacket;
	strPacket = "";
	strPacket.reserve(0);
	string key;
	char szCode[8];

	memset(szCode		,0x00	,sizeof(szCode));

	sprintf(szCode,"%d",nCode);

	key = mmsPacketSend.getKeyValue();

	strPacket = "BEGIN ACK\r\nKEY:" + key + "\r\nCODE:" ;
	strPacket += szCode;
	strPacket += "\r\nDESC:" + strDesc + "\r\nEND\r\n";
		
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	logPrintS(1,"[INF] send ack ctnid[%d]key[%s]code[%s]strDesc[%s]", 
		ctnid, key.c_str(), szCode, strDesc.c_str() );

	return ret;
}

int send_image_url_ack (CKSSocket& hRemoteSock,
                        CMMSPacketSend& mmsPacketSend,
                        string strCode,
                        string strDesc,
                        string strImgLink)
{
	int res = 0;
	char szCode[8];
	string strPacket;
	strPacket = "";
	strPacket.reserve(0);
	string key;
	key = mmsPacketSend.getKeyValue();

	memset(szCode, 0x00, sizeof(szCode));

	strPacket = "BEGIN ACK\r\nKEY:" + key + "\r\nCODE:" + strCode +
			"\r\nDESC:" + strDesc + "\r\nIMGURL:" + strImgLink +
			"\r\nEND\r\n";

	res = hRemoteSock.send((char*) strPacket.c_str(), strPacket.length());
	logPrintS(1, "[%s()][INF] send ftk image url ack. key: %s, code: %s, "\
			"strDesc: %s, img_url: %s", __func__, key.c_str(), strCode.c_str(),
			strDesc.c_str(), strImgLink.c_str());

	return res;
}

int sendPong(CKSSocket& hRemoteSock)
{
	string strPacket;
	string strKey;
	CMMSPacketBase packetBase;
	int ret;
	
	packetBase.findValue((char*)hRemoteSock.getMsg(),"KEY",strKey);

	strPacket = "BEGIN PONG\r\nKEY:" + strKey + "\r\nEND\r\n";
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	
	if( ret != strPacket.length() )
	{
		logPrintS(0,"[ERR] socket ack send failed sendSize/packetSize[%d/%d]",ret,strPacket.length());
		return ret;
	}
	//logPrintS(0,"[INF] socket link PONG send");
	logPrintS(0,"[INF] SENDER PONG send");
  
	fflush(stdout);
	return 0;
}

/** @return negative value terminates process */
int recvLink(CKSSocket& hRemoteSock,char* buff)
{
	int ret;

	TypeMsgDataAck* pLinkAck = (TypeMsgDataAck*)buff;
	memset(pLinkAck->header.msgType, 0x00, sizeof(pLinkAck->header.msgType));
	strcpy(pLinkAck->header.msgType,"8");

	ret = hRemoteSock.send(buff,sizeof(TypeMsgDataAck));
	if( ret != sizeof(TypeMsgDataAck))
	{
		logPrintS(0,"[ERR] socket link ack send failed - errno[%s] sendSize/packetSize[%d/%d]", strerror(errno), ret, sizeof(TypeMsgDataAck));
		return -1;
	}
	time(&SLastTLink);

	return 0;
}

void logPrintS(int type, const char *format, ...)
{
	va_list args;
	char logMsg[SOCKET_BUFF];
	char tmpMsg[SOCKET_BUFF];

	va_start(args, format);
	vsprintf(tmpMsg, format, args);
	va_end(args);

	sprintf(logMsg,"[S][%s] %s",szSenderID,tmpMsg);
	if (type==1)
	{
		_logPrint(_DATALOG,logMsg);
	}
	else
	{
		_monPrint(_MONILOG,logMsg);
	}
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;

	logPrintS(0,"[INF] configParse START - file[%s]", file);

	if((pEntry = conf.qfDecoder(file)) == NULL)
	{
		logPrintS(0,"[ERR] Configuration file(%s) not found or parse failed", file);
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	logPrintS(0,"[INF] configParse - qfDecoder SUCCESS");

	char* logonDBNameValue = conf.FetchEntry("domain.logondb");
	if( logonDBNameValue == NULL )
	{
		strcpy(gConf.logonDBName,"");
	}
	else
	{
		conf.strncpy2(gConf.logonDBName, logonDBNameValue, 64);
	}
	logPrintS(0,"[INF] configParse - logonDBName[%s]", gConf.logonDBName);

	char* monitorNameValue = conf.FetchEntry("domain.monitor");
	if( monitorNameValue == NULL )
	{
		strcpy(gConf.monitorName,"");
	}
	else
	{
		conf.strncpy2(gConf.monitorName, monitorNameValue, 64);
	}
	logPrintS(0,"[INF] configParse - monitorName[%s]", gConf.monitorName);

	char* domainPathValue = conf.FetchEntry("domain.self");
	if( domainPathValue == NULL )
	{
		strcpy(gConf.domainPath,"");
	}
	else
	{
		conf.strncpy2(gConf.domainPath, domainPathValue, 64);
	}
	logPrintS(0,"[INF] configParse - domainPath[%s]", gConf.domainPath);

	char* contentPathValue = conf.FetchEntry("path.mmscontent");
	if( contentPathValue == NULL )
	{
		strcpy(gConf.ContentPath,"");
	}
	else
	{
		conf.strncpy2(gConf.ContentPath, contentPathValue, 64);
	}
	logPrintS(0,"[INF] configParse - ContentPath[%s]", gConf.ContentPath);

	gConf.socketLinkTimeOut = conf.FetchEntryInt("socket.linktimeout");

	if( gConf.socketLinkTimeOut <= 1 )
	{
		gConf.socketLinkTimeOut = 2;
	}
	logPrintS(0,"[INF] configParse - socketLinkTimeOut[%d]", gConf.socketLinkTimeOut);

	gConf.dbRequestTimeOut = conf.FetchEntryInt("db.requesttimeout");

	if( gConf.dbRequestTimeOut <= 0 )
	{
		gConf.dbRequestTimeOut = 1;
	}
	logPrintS(0,"[INF] configParse - dbRequestTimeOut[%d]", gConf.dbRequestTimeOut);

	char* dbuidValue = conf.FetchEntry("db.uid");
	if( dbuidValue == NULL )
	{
		strcpy(gConf.dbuid,"");
	}
	else
	{
		conf.strncpy2(gConf.dbuid, dbuidValue, 64);
	}
	logPrintS(0,"[INF] configParse - dbuid[%s]", gConf.dbuid);

	char* dbdsnValue = conf.FetchEntry("db.dsn");
	if( dbdsnValue == NULL )
	{
		strcpy(gConf.dbdsn,"");
	}
	else
	{
		conf.strncpy2(gConf.dbdsn, dbdsnValue, 64);
	}
	logPrintS(0,"[INF] configParse - dbdsn[%s]", gConf.dbdsn);
	
	gConf.dbMmsIdHeader = conf.FetchEntryInt("db.mmsidhead");

	if( gConf.dbMmsIdHeader <= 0 )
	{
		gConf.dbMmsIdHeader = 0;
	}
	logPrintS(0,"[INF] configParse - dbMmsIdHeader[%d]", gConf.dbMmsIdHeader);

	gConf.gwAppHeader = conf.FetchEntryInt("gw.apphead");

	if( gConf.gwAppHeader <= 0 )
	{
		gConf.gwAppHeader = 0;
	}
	logPrintS(0,"[INF] configParse - gwAppHeader[%d]", gConf.gwAppHeader);

	gConf.gwAddTelco = conf.FetchEntryInt("gw.addtelco");

	if( gConf.gwAddTelco <= 0 )
	{
		gConf.gwAddTelco = 0;
	}
	logPrintS(0,"[INF] configParse - gwAddTelco[%d]", gConf.gwAddTelco);

	char* encryptKeyValue = conf.FetchEntry("encrypt.key");
	if( encryptKeyValue == NULL )
	{
		strcpy(gConf.encryptKey,"");  // set default value
	}
	else
	{
		conf.strncpy2(gConf.encryptKey, encryptKeyValue, 64);
	}
	logPrintS(0,"[INF] configParse - encryptKey[%s]", gConf.encryptKey);

	logPrintS(0,"[INF] configParse SUCCESS - all config loaded");
	return 0;
}


void viewPackSender(char *a,int n)
{
	int i;
	char logMsg[VIEWPACK_MAX_SIZE];
	char strtmp[VIEWPACK_MAX_SIZE];
	
	memset(logMsg,0x00, sizeof logMsg);
	memset(strtmp,0x00, sizeof strtmp);
	
	for(i=0;i<n;i++)
	{
		if( a[i] == 0x00 )
		{
			strtmp[i] = '.';
	    }
	    else
		{
			memcpy(strtmp+i,a+i,1);
		}
	}
	
	sprintf(logMsg,"info:[%s]",strtmp);
	_monPrint(_MONILOG,logMsg);
	
	return ;
}

int setMMSCTNTBL2DB(CMMSFileProcess& mmsFileProcess)
{
	int ret;
	CSenderDbMMSCTNTBL senderDbMMSCTNTBL;

	CMMSCtnTbl mmsCtnTbl;

	ret = mmsFileProcess.getMMSCtnTblFirst(mmsCtnTbl);
	if( ret != 0 ) return 1;

	while(ret == 0 )
	{
		memset(&senderDbMMSCTNTBL,0x00,sizeof(CSenderDbMMSCTNTBL));
		senderDbMMSCTNTBL.nCtnId = mmsCtnTbl.nCtnId;
		memcpy(senderDbMMSCTNTBL.szCtnName, mmsCtnTbl.szCtnName, sizeof(mmsCtnTbl.szCtnName));
		memcpy(senderDbMMSCTNTBL.szCtnMime, mmsCtnTbl.szCtnMime, sizeof(mmsCtnTbl.szCtnMime));
		senderDbMMSCTNTBL.nCtnSeq = mmsCtnTbl.nCtnSeq;
		memcpy(senderDbMMSCTNTBL.szCtnSvc , mmsCtnTbl.szCtnSvc , sizeof(mmsCtnTbl.szCtnSvc ));

		ret = g_oracle.setMMSCTNTBL(senderDbMMSCTNTBL);
		if (ret <= 0)
		{
			logPrintS(0,"[ERR] setMMSCTNTBL [%d]", ret);
		}
		ret = mmsFileProcess.getMMSCtnTblNext(mmsCtnTbl);
	}

	return 0; // OK
}

#if (0)
//int setMMSMSG2DB_TALK(long long nMMSId,CMMSPacketSend& mmsPacketSend, int priority)
int setMMSMSG2DB_FTK(long long nMMSId,CMMSPacketSend& mmsPacketSend, int priority)
{
	int ret, size;
    char szType[50+1];
    char* pData;
    CMData mData;
	CSenderDbMMSMSG_FTALK senderDbMMSMSG;
		
    ret = mmsPacketSend.getMDataFirst(mData);
    if( ret != 0 )
	{
        logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}
	memset(szType, 0x00, sizeof(szType));
    sprintf(szType,(char*)mData.contentType.strType.c_str());
    if (strcmp(szType, "TXT") == 0)
    {
    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
    }
    else
    {
    	while((  ret = mmsPacketSend.getMDataNext(mData)) == 0 )
	    {
	        memset(szType,0x00,sizeof(szType));	
	        sprintf(szType,(char*)mData.contentType.strType.c_str());
		    if (strcmp(szType, "TXT") == 0)
		    {
		    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
		    	break;
		    }
	    }
    }
	
	memset(&senderDbMMSMSG, 0x00, sizeof(CSenderDbMMSMSG_FTALK));
	
	//encryption check
	if(strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), " aes_base64", 11) == 0) 
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key_from_config(gConf.encryptKey);
		en.decrypt(receiverNum, receiverNum, strlen((char*)receiverNum));
		sprintf(senderDbMMSMSG.szDstAddr ,"82%s", string((char*)receiverNum).substr(1).c_str());
		////logPrintS(1,"[INFO] [%s]", senderDbMMSMSG.szDstAddr);
	
		//decrypt message
		//logPrintS(1,"[INFO] pData[%d]", strlen((char*)pData));
		en.decrypt((unsigned char*)pData, (unsigned char*)pData, atoi(mmsPacketSend.getMsgOrgSizeValue()));
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), (char*)pData);
		strncpy(senderDbMMSMSG.szMsgBody, (char*)pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
		
		//logPrintS(1,"[INFO] szMsgBody[%s]", senderDbMMSMSG.szMsgBody);
		free(receiverNum);
	}
	else
	{
		sprintf(senderDbMMSMSG.szDstAddr ,"82%s", mmsPacketSend.getReceiverValue()+1);
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), pData);
		strncpy(senderDbMMSMSG.szMsgBody, pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
	}
	

	//sprintf(senderDbMMSMSG.szQName,"%d", getTelcoId(mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, 0));
	sprintf(senderDbMMSMSG.szQName,"%d", 
			getTelcoId(mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, gSenderInfo.szQType));
	//strcpy(senderDbMMSMSG.szSenderKey ,mmsPacketSend.getSenderKeyValue());
	strncpy(senderDbMMSMSG.szSenderKey ,mmsPacketSend.getSenderKeyValue(),sizeof(senderDbMMSMSG.szSenderKey)-1);
	
	if(strncmp(mmsPacketSend.getBtNameValue(), "null", 4) == 0)
	{
		snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), "");
		snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), "");
	}
	else
	{
		snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), mmsPacketSend.getBtNameValue());
		//snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), mmsPacketSend.getBtUrlValue());
		//strcpy(senderDbMMSMSG.szBtUrl, mmsPacketSend.getBtUrlValue());
		strncpy(senderDbMMSMSG.szBtUrl,  mmsPacketSend.getBtUrlValue(),sizeof(senderDbMMSMSG.szBtUrl)-1);
	}
	
	//snprintf(senderDbMMSMSG.szButton, sizeof(senderDbMMSMSG.szButton), mmsPacketSend.getButtonValue());
	strncpy(senderDbMMSMSG.szButton, mmsPacketSend.getButtonValue(), sizeof(senderDbMMSMSG.szButton)-1);	
	
	//logPrintS(1,"SHS Button char length[%d] ",strlen(mmsPacketSend.getButtonValue()));
	//logPrintS(1,"SHS Button char size[%d] ",sizeof(mmsPacketSend.getButtonValue()));
	//logPrintS(1,"SHS Button senderDbMMSMSG char length[%d] ",strlen(senderDbMMSMSG.szButton));
	//logPrintS(1,"SHS Button senderDbMMSMSG char size[%d] ",sizeof(senderDbMMSMSG.szButton));
	
	//snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), mmsPacketSend.getBtNameValue());
	//snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), mmsPacketSend.getBtUrlValue());
	snprintf(senderDbMMSMSG.szImgPath, sizeof(senderDbMMSMSG.szImgPath), mmsPacketSend.getImgPathValue());
	//snprintf(senderDbMMSMSG.szImgLink, sizeof(senderDbMMSMSG.szImgLink), mmsPacketSend.getImgLinkValue());
	strncpy(senderDbMMSMSG.szImgLink, mmsPacketSend.getImgLinkValue(), sizeof(senderDbMMSMSG.szImgLink)-1);
	snprintf(senderDbMMSMSG.szUserKey, sizeof(senderDbMMSMSG.szUserKey), mmsPacketSend.getUserKeyValue());

	snprintf(senderDbMMSMSG.szResMethod, sizeof(senderDbMMSMSG.szResMethod), gSenderInfo.szResMethod);
	snprintf(senderDbMMSMSG.szTimeout, sizeof(senderDbMMSMSG.szTimeout), gSenderInfo.szTimeout);

	senderDbMMSMSG.nMMSId = nMMSId;
	senderDbMMSMSG.nPriority = priority;
	
	if(strlen(mmsPacketSend.getAdFlagValue()) == 0 || strncmp(mmsPacketSend.getAdFlagValue(), "null", 4) == 0)
	{
		strncpy(senderDbMMSMSG.szAdFlag, "Y", sizeof(senderDbMMSMSG.szAdFlag)-1);
	}
	else
	{
		strncpy(senderDbMMSMSG.szAdFlag, mmsPacketSend.getAdFlagValue(), sizeof(senderDbMMSMSG.szAdFlag)-1);
	}
	
	if(strcmp(mmsPacketSend.getWideValue(), "Y") == 0 )
	{
		strncpy(senderDbMMSMSG.szWide, mmsPacketSend.getWideValue(), sizeof(senderDbMMSMSG.szWide)-1);
	}
	else
	{
		strncpy(senderDbMMSMSG.szWide, "N", sizeof(senderDbMMSMSG.szWide)-1);		
	}

	strncpy(senderDbMMSMSG.szKkoImgUrl, mmsPacketSend.getKkoImgUrlValue(),
			sizeof(senderDbMMSMSG.szKkoImgUrl) - 1);

	free(pData);

	//ret = g_oracle.setMMSMSG_FTALK_V2(senderDbMMSMSG);
	ret = g_oracle.setMMSMSG_FTK_V3(senderDbMMSMSG);
	if (ret <= 0)
	{
		//logPrintS(0,"[ERR] setMMSMSG_FTALK_V2 MMSID[%lld]ret[%d]", nMMSId, ret);
		logPrintS(0,"[ERR] setMMSMSG_FTALK_V3 MMSID[%lld]ret[%d]", nMMSId, ret);
		return ret;
	}
	else
	{
		ret = 100;	
		return ret;
	}		
}
#endif

int setMMSMSG2DB_FTKUP(long long nMMSId,CMMSPacketSend& mmsPacketSend, int priority)
{
	int ret, size;
    char szType[50+1];
    char* pData;
    CMData mData;
	CSenderDbMMSMSG_FTALKUP senderDbMMSMSG;
		
    //ret = mmsPacketSend.getMDataFirst(mData);
    //if( ret != 0 )
	//{
    //    logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
	//	return -1;
	//}
	//memset(szType, 0x00, sizeof(szType));
    //sprintf(szType,(char*)mData.contentType.strType.c_str());
    //if (strcmp(szType, "TXT") == 0)
    //{
    //	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
    //}
    //else
    //{
    //	while((  ret = mmsPacketSend.getMDataNext(mData)) == 0 )
	//    {
	//        memset(szType,0x00,sizeof(szType));	
	//        sprintf(szType,(char*)mData.contentType.strType.c_str());
	//	    if (strcmp(szType, "TXT") == 0)
	//	    {
	//	    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
	//	    	break;
	//	    }
	//    }
    //}
	
	memset(&senderDbMMSMSG, 0x00, sizeof(CSenderDbMMSMSG_FTALK));

	
	//encryption check
	//if(strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), " aes_base64", 11) == 0)
	//if (strncmp(mmsPacketSend.getEncodingValue(), "aes_base64", 10) == 0)
	if (strncmp(mmsPacketSend.getEncryptValue(), "aes_base64", 10) == 0)
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key_from_config(gConf.encryptKey);
		en.decrypt(receiverNum, receiverNum, strlen((char*)receiverNum));
		sprintf(senderDbMMSMSG.szDstAddr ,"82%s", string((char*)receiverNum).substr(1).c_str());
		////logPrintS(1,"[INFO] [%s]", senderDbMMSMSG.szDstAddr);
	
		//decrypt message
		//logPrintS(1,"[INFO] pData[%d]", strlen((char*)pData));
		//en.decrypt((unsigned char*)pData, (unsigned char*)pData, atoi(mmsPacketSend.getMsgOrgSizeValue()));
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), (char*)pData);
		//strncpy(senderDbMMSMSG.szMsgBody, (char*)pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
		
		//logPrintS(1,"[INFO] szMsgBody[%s]", senderDbMMSMSG.szMsgBody);
		free(receiverNum);
	}
	else
	{
		sprintf(senderDbMMSMSG.szDstAddr ,"82%s", mmsPacketSend.getReceiverValue()+1);
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), pData);
		//strncpy(senderDbMMSMSG.szMsgBody, pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
	}
	
	//strcpy(senderDbMMSMSG.szSenderKey ,mmsPacketSend.getSenderKeyValue());
	strncpy(senderDbMMSMSG.szSenderKey ,mmsPacketSend.getSenderKeyValue(),sizeof(senderDbMMSMSG.szSenderKey)-1);
	
	snprintf(senderDbMMSMSG.szResMethod, sizeof(senderDbMMSMSG.szResMethod), gSenderInfo.szResMethod);
	snprintf(senderDbMMSMSG.szTimeout, sizeof(senderDbMMSMSG.szTimeout), gSenderInfo.szTimeout);

	senderDbMMSMSG.nMMSId = nMMSId;
	senderDbMMSMSG.nPriority = priority;

	snprintf(senderDbMMSMSG.szEncoding, sizeof(senderDbMMSMSG.szEncoding), mmsPacketSend.getEncodingValue());
	snprintf(senderDbMMSMSG.szChatBubbleType, sizeof(senderDbMMSMSG.szChatBubbleType), mmsPacketSend.getChatTypeValue());	
	snprintf(senderDbMMSMSG.szTargeting, sizeof(senderDbMMSMSG.szTargeting), mmsPacketSend.getTargetingValue());	
	snprintf(senderDbMMSMSG.szTmplCd, sizeof(senderDbMMSMSG.szTmplCd),mmsPacketSend.getTmplCdValue());
	snprintf(senderDbMMSMSG.szAppUserId, sizeof(senderDbMMSMSG.szAppUserId), mmsPacketSend.getAppUserIdValue());
	snprintf(senderDbMMSMSG.szPushAlarm, sizeof(senderDbMMSMSG.szPushAlarm), mmsPacketSend.getPushAlarmValue());
	snprintf(senderDbMMSMSG.szMessageVariable, sizeof(senderDbMMSMSG.szMessageVariable),
			mmsPacketSend.getMsgBodyValue());
	snprintf(senderDbMMSMSG.szButtonVariable, sizeof(senderDbMMSMSG.szButtonVariable),
			mmsPacketSend.getButtonVariableValue());
	snprintf(senderDbMMSMSG.szCouponVariable, sizeof(senderDbMMSMSG.szCouponVariable),
			mmsPacketSend.getCouponVariableValue());
	snprintf(senderDbMMSMSG.szImageVariable, sizeof(senderDbMMSMSG.szImageVariable),
			mmsPacketSend.getImageVariableValue());
	snprintf(senderDbMMSMSG.szVideoVariable, sizeof(senderDbMMSMSG.szVideoVariable),
			mmsPacketSend.getVideoVariableValue());
	snprintf(senderDbMMSMSG.szCommerceVariable, sizeof(senderDbMMSMSG.szCommerceVariable),
			mmsPacketSend.getCommerceVariableValue());
	snprintf(senderDbMMSMSG.szCarouselVariable, sizeof(senderDbMMSMSG.szCarouselVariable),
			mmsPacketSend.getCarouselVariableValue());
	snprintf(senderDbMMSMSG.szReserve, sizeof(senderDbMMSMSG.szReserve),
			mmsPacketSend.getReserveValue());
	
	//free(pData);

	//ret = g_oracle.setMMSMSG_FTALK_V2(senderDbMMSMSG);
	//ret = g_oracle.setMMSMSG_FTK_V3(senderDbMMSMSG);
	ret = g_oracle.setMMSMSG_FTKUP(senderDbMMSMSG);
	if (ret <= 0)
	{
		//logPrintS(0,"[ERR] setMMSMSG_FTALK_V2 MMSID[%lld]ret[%d]", nMMSId, ret);
		logPrintS(0,"[ERR] setMMSMSG_FTKUP MMSID[%lld]ret[%d]", nMMSId, ret);
		return ret;
	}
	else
	{
		ret = 100;	
		return ret;
	}		
}


int setMMSTBL2DB(long long nMMSId, int ctnid,int priority,CBcastData * pBcastData)
{
	int ret;
	char szMsgTitle[200+1];
	CSenderDbMMSTBL senderDbMMSTBL;
	CMData mData;
	/*
	ret = mmsPacketSend.getMDataFirst(mData);
	if( ret != 0 )
	{
		logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}
	*/
	
	memset(szMsgTitle,0x00,sizeof(szMsgTitle));
	memset(&senderDbMMSTBL,0x00,sizeof(CSenderDbMMSTBL));

	//if (strlen(mmsPacketSend.getSenderValue()) <= 0)
	//	strcpy(senderDbMMSTBL.szCallBack, "0");
	//else
	//	strcpy(senderDbMMSTBL.szCallBack, mmsPacketSend.getSenderValue());

	// Check if receiver number is encrypted and decrypt if necessary
	if (strncmp(mmsPacketSend.getEncryptValue(), "aes_base64", 10) == 0)
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key_from_config(gConf.encryptKey);
		en.decrypt(receiverNum, receiverNum, strlen((char*)receiverNum));
		strcpy(senderDbMMSTBL.szDstAddr, (char*)receiverNum);
		logPrintS(1,"[INFO] Decrypted receiver: [%s]", senderDbMMSTBL.szDstAddr);
		free(receiverNum);
	}
	else
	{
		strcpy(senderDbMMSTBL.szDstAddr , mmsPacketSend.getReceiverValue());
	}
	
	//strcpy(senderDbMMSTBL.szMsgTitle, mmsPacketSend.getSubjectValue() );
	strcpy(senderDbMMSTBL.szPtnSn   , mmsPacketSend.getKeyValue()     );
	//strcpy(senderDbMMSTBL.szResvData, mmsPacketSend.getExtendValue()  );
	strcpy(senderDbMMSTBL.szCid     , szSenderID                      );
	senderDbMMSTBL.nMsgType 	= 1;		/* 0: LMS|MMS, 1: Alim Talk */
	//senderDbMMSTBL.nImgCnt 		= mmsPacketSend.getImgCnt();

    #if 0
	if (strlen(mmsPacketSend.getKkoImgUrlValue()) > 0|| ctnid > 0 ) {
		/* image */
		if (0 == strcmp(mmsPacketSend.getWideValue(), "Y")) {
			senderDbMMSTBL.nMsgType = 23;
			senderDbMMSTBL.nImgCnt = 99;
		} else {
			senderDbMMSTBL.nMsgType = 22;
			senderDbMMSTBL.nImgCnt = 1;
		}
	} else {
		// if there was no content id, it would be a text type message.
		senderDbMMSTBL.nMsgType = 21;
	}
	#endif

	senderDbMMSTBL.nPriority 	= priority;
	senderDbMMSTBL.nCtnId 		= ctnid;
	//senderDbMMSTBL.nCtnType 	= mmsPacketSend.getCtnType();
	senderDbMMSTBL.nRgnRate 	= 65;
	senderDbMMSTBL.nInterval 	= 10;
	//senderDbMMSTBL.nTextCnt 	= mmsPacketSend.getTextCnt();
	//senderDbMMSTBL.nAugCnt 		= mmsPacketSend.getAugCnt();
	//senderDbMMSTBL.nMpCnt 		= mmsPacketSend.getMpCnt();
	senderDbMMSTBL.nMMSId 		= nMMSId;
	
	//20190708 senderkey added
	strncpy(senderDbMMSTBL.szSenderKey ,mmsPacketSend.getSenderKeyValue(),sizeof(senderDbMMSTBL.szSenderKey)-1);
	
	strncpy(senderDbMMSTBL.szChatBubbleType, 
		mmsPacketSend.getChatTypeValue(), sizeof(senderDbMMSTBL.szChatBubbleType) - 1);
	
	strncpy(senderDbMMSTBL.szTargeting, 
		mmsPacketSend.getTargetingValue(), sizeof(senderDbMMSTBL.szTargeting) - 1);
	
	strncpy(senderDbMMSTBL.szTmplCd, 
		mmsPacketSend.getTmplCdValue(), sizeof(senderDbMMSTBL.szTmplCd) - 1);
	
	//strncpy(senderDbMMSTBL.szRepFlag, 
	//	mmsPacketSend.getRepFlagValue(), sizeof(senderDbMMSTBL.szRepFlag) - 1);
	
	//strncpy(senderDbMMSTBL.szRepTitle, 
	//	mmsPacketSend.getRepMsgTitleValue(), sizeof(senderDbMMSTBL.szRepTitle) - 1);
	
	//strncpy(senderDbMMSTBL.szRepText, 
	//	mmsPacketSend.getRepMsgBodyValue(), sizeof(senderDbMMSTBL.szRepText) - 1);

	strncpy(senderDbMMSTBL.szAppUserId,
		mmsPacketSend.getAppUserIdValue(), sizeof(senderDbMMSTBL.szAppUserId) - 1);

	strncpy(senderDbMMSTBL.szPushAlarm,
		mmsPacketSend.getPushAlarmValue(), sizeof(senderDbMMSTBL.szPushAlarm) - 1);

	// encoding verification and conversion processing
	const char* encoding = mmsPacketSend.getEncodingValue();
	bool isUtf8 = (encoding && (strcmp(encoding, "utf8") == 0 || strcmp(encoding, "UTF-8") == 0));

	// Check if conversion is needed based on client encoding and database charset
	bool needsConversion = isUtf8 && g_charset_checked && g_db_is_euckr;

	if (isUtf8) {
		if (g_charset_checked) {
			logPrintS(1,"[INF] UTF-8 encoding detected, DB charset[%s], conversion needed[%s] for MMSID[%lld]",
			         g_db_charset, needsConversion ? "YES" : "NO", nMMSId);
		} else {
			logPrintS(1,"[INF] UTF-8 encoding detected, DB charset unknown, assuming conversion needed for MMSID[%lld]", nMMSId);
			needsConversion = true; // Default to conversion if charset not checked
		}
	}

	// MessageVariable processing
	if (needsConversion) {
		char tempBuffer[sizeof(senderDbMMSTBL.szMessageVariable)];
		memset(tempBuffer, 0x00, sizeof(tempBuffer));
		//if (utf8ToEuckr(mmsPacketSend.getMsgBodyValue(), tempBuffer, sizeof(tempBuffer)) > 0) {
		if (utf8ToEuckr(mmsPacketSend.getMessageVariableValue(), tempBuffer, sizeof(tempBuffer)) > 0) {	
			strncpy(senderDbMMSTBL.szMessageVariable, tempBuffer, sizeof(senderDbMMSTBL.szMessageVariable) - 1);
			logPrintS(1,"[INF] UTF-8 to EUC-KR conversion successful for MessageVariable MMSID[%lld]", nMMSId);
		} else {
			logPrintS(1,"[WARN] UTF-8 to EUC-KR conversion failed for MessageVariable, using original data MMSID[%lld]", nMMSId);
			strncpy(senderDbMMSTBL.szMessageVariable, mmsPacketSend.getMsgBodyValue(), sizeof(senderDbMMSTBL.szMessageVariable) - 1);
		}
	} else {
		strncpy(senderDbMMSTBL.szMessageVariable, mmsPacketSend.getMsgBodyValue(), sizeof(senderDbMMSTBL.szMessageVariable) - 1);
	}

	// ButtonVariable processing
	if (needsConversion) {
		char tempBuffer[sizeof(senderDbMMSTBL.szButtonVariable)];
		memset(tempBuffer, 0x00, sizeof(tempBuffer));
		if (utf8ToEuckr(mmsPacketSend.getButtonVariableValue(), tempBuffer, sizeof(tempBuffer)) > 0) {
			strncpy(senderDbMMSTBL.szButtonVariable, tempBuffer, sizeof(senderDbMMSTBL.szButtonVariable) - 1);
			logPrintS(1,"[INF] UTF-8 to EUC-KR conversion successful for ButtonVariable MMSID[%lld]", nMMSId);
		} else {
			logPrintS(1,"[WARN] UTF-8 to EUC-KR conversion failed for ButtonVariable, using original data MMSID[%lld]", nMMSId);
			strncpy(senderDbMMSTBL.szButtonVariable, mmsPacketSend.getButtonVariableValue(), sizeof(senderDbMMSTBL.szButtonVariable) - 1);
		}
	} else {
		strncpy(senderDbMMSTBL.szButtonVariable, mmsPacketSend.getButtonVariableValue(), sizeof(senderDbMMSTBL.szButtonVariable) - 1);
	}

	// CouponVariable processing
	if (needsConversion) {
		char tempBuffer[sizeof(senderDbMMSTBL.szCouponVariable)];
		memset(tempBuffer, 0x00, sizeof(tempBuffer));
		if (utf8ToEuckr(mmsPacketSend.getCouponVariableValue(), tempBuffer, sizeof(tempBuffer)) > 0) {
			strncpy(senderDbMMSTBL.szCouponVariable, tempBuffer, sizeof(senderDbMMSTBL.szCouponVariable) - 1);
			logPrintS(1,"[INF] UTF-8 to EUC-KR conversion successful for CouponVariable MMSID[%lld]", nMMSId);
		} else {
			logPrintS(1,"[WARN] UTF-8 to EUC-KR conversion failed for CouponVariable, using original data MMSID[%lld]", nMMSId);
			strncpy(senderDbMMSTBL.szCouponVariable, mmsPacketSend.getCouponVariableValue(), sizeof(senderDbMMSTBL.szCouponVariable) - 1);
		}
	} else {
		strncpy(senderDbMMSTBL.szCouponVariable, mmsPacketSend.getCouponVariableValue(), sizeof(senderDbMMSTBL.szCouponVariable) - 1);
	}

	// ImageVariable processing
	if (needsConversion) {
		char tempBuffer[sizeof(senderDbMMSTBL.szImageVariable)];
		memset(tempBuffer, 0x00, sizeof(tempBuffer));
		if (utf8ToEuckr(mmsPacketSend.getImageVariableValue(), tempBuffer, sizeof(tempBuffer)) > 0) {
			strncpy(senderDbMMSTBL.szImageVariable, tempBuffer, sizeof(senderDbMMSTBL.szImageVariable) - 1);
			logPrintS(1,"[INF] UTF-8 to EUC-KR conversion successful for ImageVariable MMSID[%lld]", nMMSId);
		} else {
			logPrintS(1,"[WARN] UTF-8 to EUC-KR conversion failed for ImageVariable, using original data MMSID[%lld]", nMMSId);
			strncpy(senderDbMMSTBL.szImageVariable, mmsPacketSend.getImageVariableValue(), sizeof(senderDbMMSTBL.szImageVariable) - 1);
		}
	} else {
		strncpy(senderDbMMSTBL.szImageVariable, mmsPacketSend.getImageVariableValue(), sizeof(senderDbMMSTBL.szImageVariable) - 1);
	}

	// VideoVariable processing
	if (needsConversion) {
		char tempBuffer[sizeof(senderDbMMSTBL.szVideoVariable)];
		memset(tempBuffer, 0x00, sizeof(tempBuffer));
		if (utf8ToEuckr(mmsPacketSend.getVideoVariableValue(), tempBuffer, sizeof(tempBuffer)) > 0) {
			strncpy(senderDbMMSTBL.szVideoVariable, tempBuffer, sizeof(senderDbMMSTBL.szVideoVariable) - 1);
			logPrintS(1,"[INF] UTF-8 to EUC-KR conversion successful for VideoVariable MMSID[%lld]", nMMSId);
		} else {
			logPrintS(1,"[WARN] UTF-8 to EUC-KR conversion failed for VideoVariable, using original data MMSID[%lld]", nMMSId);
			strncpy(senderDbMMSTBL.szVideoVariable, mmsPacketSend.getVideoVariableValue(), sizeof(senderDbMMSTBL.szVideoVariable) - 1);
		}
	} else {
		strncpy(senderDbMMSTBL.szVideoVariable, mmsPacketSend.getVideoVariableValue(), sizeof(senderDbMMSTBL.szVideoVariable) - 1);
	}

	// CommerceVariable processing
	if (needsConversion) {
		char tempBuffer[sizeof(senderDbMMSTBL.szCommerceVariable)];
		memset(tempBuffer, 0x00, sizeof(tempBuffer));
		if (utf8ToEuckr(mmsPacketSend.getCommerceVariableValue(), tempBuffer, sizeof(tempBuffer)) > 0) {
			strncpy(senderDbMMSTBL.szCommerceVariable, tempBuffer, sizeof(senderDbMMSTBL.szCommerceVariable) - 1);
			logPrintS(1,"[INF] UTF-8 to EUC-KR conversion successful for CommerceVariable MMSID[%lld]", nMMSId);
		} else {
			logPrintS(1,"[WARN] UTF-8 to EUC-KR conversion failed for CommerceVariable, using original data MMSID[%lld]", nMMSId);
			strncpy(senderDbMMSTBL.szCommerceVariable, mmsPacketSend.getCommerceVariableValue(), sizeof(senderDbMMSTBL.szCommerceVariable) - 1);
		}
	} else {
		strncpy(senderDbMMSTBL.szCommerceVariable, mmsPacketSend.getCommerceVariableValue(), sizeof(senderDbMMSTBL.szCommerceVariable) - 1);
	}

	// CarouselVariable processing
	if (needsConversion) {
		char tempBuffer[sizeof(senderDbMMSTBL.szCarouselVariable)];
		memset(tempBuffer, 0x00, sizeof(tempBuffer));
		if (utf8ToEuckr(mmsPacketSend.getCarouselVariableValue(), tempBuffer, sizeof(tempBuffer)) > 0) {
			strncpy(senderDbMMSTBL.szCarouselVariable, tempBuffer, sizeof(senderDbMMSTBL.szCarouselVariable) - 1);
			logPrintS(1,"[INF] UTF-8 to EUC-KR conversion successful for CarouselVariable MMSID[%lld]", nMMSId);
		} else {
			logPrintS(1,"[WARN] UTF-8 to EUC-KR conversion failed for CarouselVariable, using original data MMSID[%lld]", nMMSId);
			strncpy(senderDbMMSTBL.szCarouselVariable, mmsPacketSend.getCarouselVariableValue(), sizeof(senderDbMMSTBL.szCarouselVariable) - 1);
		}
	} else {
		strncpy(senderDbMMSTBL.szCarouselVariable, mmsPacketSend.getCarouselVariableValue(), sizeof(senderDbMMSTBL.szCarouselVariable) - 1);
	}

	ret = g_oracle.setMMSTBL(senderDbMMSTBL);

	if (ret <= 0)
	{
		logPrintS(0,"[ERR] setMMSTBL MMSID[%lld]CID[%s]ret[%d]", nMMSId,szSenderID, ret);
	}
	
	return ret;
}


/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
int getCTNID2DB(CSenderDbMMSID& senderDbMMSID)
{
	int ret = 0;
	if(mmsPacketSend.getImgCnt() == 0)
	{
		senderDbMMSID.ctnid = -1;
		return 0;
	}

	senderDbMMSID.ctnid = g_oracle.getCTNID();
	if(senderDbMMSID.ctnid <= 0 )
	{
		logPrintS(0,"[ERR] socket_domain get ctn id to db failed - ctnid[%d]", senderDbMMSID.ctnid);
		// 2013.12 LSY needs improvement
		senderDbMMSID.ctnid = 999999999;

		return 0;
	}
	if(senderDbMMSID.ctnid <= 0)
	{
		return -1;
	}
	return 1;
}

/**====================================================================================**/
// 2014020 make mmsid
// 20170621 MMSID seq use
/**====================================================================================**/
long long mkMMSID(int mmsIdHeader)
{

	char	pch[30];
	char	pchlast[30];
	
	time_t	the_time;
	struct	timeval val;
	struct	tm	*tm_ptr;
	
	long long ulltm;
	
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);

	memset(pch				,0x00		,sizeof(pch));	
	sprintf(pch, "%2d%02d%02d%02d%09d"
				,tp.tm_mday+10
				,tp.tm_hour
				,tp.tm_min
				,tp.tm_sec
				,(int)tmv.tv_nsec
				);

		
	memset(pchlast		,0x00		,sizeof(pchlast));
	sprintf(pchlast,"%.12s%04d", pch, proc_id);
	
	ulltm = atoll(pchlast);

	//logPrintS(1,"[INF] mkmmsid pch[%s] pchlast[%s] ulltmid[%lld]", pch, pchlast, ulltm );

	return ulltm;
	
}

/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
int getMMSID2DB(CSenderDbMMSID& senderDbMMSID, char* cid)
{
	int ret = 0;
	senderDbMMSID.mmsid = 0;
	memcpy(senderDbMMSID.szCid, cid, 10);
	senderDbMMSID.mmsid = g_oracle.getMMSID();

	/**====================================================================================**/
	// 20140212 : MOD if div
	/**====================================================================================**/
	//if(senderDbInfoAck.mmsid == 0 || senderDbInfoAck.mmsid < 0)
	/*if( senderDbMMSID.mmsid == 0 )		
	{
		logPrintS(0,"[ERR] socket_domain get mms id to db failed - mmsid[%lld]", senderDbMMSID.mmsid);
		// 2013.12 LSY needs improvement
		senderDbMMSID.mmsid = 999999999;
		
		return 0;	// 20140212 : ADD return
	}*/
	
	if(senderDbMMSID.mmsid == 0 || senderDbMMSID.mmsid < 0)
	{
		logPrintS(0,"[ERR] socket_domain get mms id to db failed - mmsid[%lld]", senderDbMMSID.mmsid);
		// 2013.12 LSY needs improvement
		//senderDbMMSID.mmsid = 999999999;
		senderDbMMSID.mmsid = mkMMSID(gConf.dbMmsIdHeader);
				
		//return 0;	// 20140212 : ADD return
		if(senderDbMMSID.mmsid == 0 || senderDbMMSID.mmsid < 0)
		return -1;
	}

	if( senderDbMMSID.mmsid < 0 )
	{
		return -1;
	}

	return 1;
	/**====================================================================================**/
}

int getTelcoId(int imgCnt, char* szTelco, int nColorYN)
{
	char* p;
	int telcoArray[5];
	int i=0;
	char szTelcoTok[30+1];
	
	memset(telcoArray,0x00,sizeof(telcoArray));
	memset(szTelcoTok,0x00,sizeof(szTelcoTok));
	
	sprintf(szTelcoTok, "%s", szTelco);

	//p = strtok(szTelco,"|");
	p = strtok(szTelcoTok,"|");
	
	if( p == NULL )
	{
		return 1;
	}
	
	if(gConf.gwAppHeader > 1)
	{
		telcoArray[i++] = (atoi(p)+gConf.gwAddTelco);
	}
	else
	{
		telcoArray[i++] = atoi(p);
	}

	while(p = strtok(NULL,"|") )
	{
		if(gConf.gwAppHeader > 1)
		{
			telcoArray[i++]= (atoi(p)+gConf.gwAddTelco);
		}else{
			telcoArray[i++]= atoi(p);
		}
		
		if( i >= 5 )
		{
			break;
		}
	}

	//sprintf(szTelco, "%d|%d|%d|%d|%d", telcoArray[0], telcoArray[1], telcoArray[2], telcoArray[3], telcoArray[4]);
	return telcoArray[4];
	
}

int getTelcoId(int imgCnt, char* szTelco, char *szQType)
{
    char* p;
    int telcoArray[6];
    int i = 0;
    char szTelcoTok[30+1];
        
    memset(telcoArray,0x00,sizeof(telcoArray));
	memset(szTelcoTok,0x00,sizeof(szTelcoTok));
	
	sprintf(szTelcoTok, "%s", szTelco);
	
	//p = strtok(szTelco,"|");
    p = strtok(szTelcoTok,"|");
    
    if( p == NULL )
    {
        return 1;
    }
    if(gConf.gwAppHeader > 1)
	{
		telcoArray[i++] = (atoi(p)+gConf.gwAddTelco);
	}
	else
	{
		telcoArray[i++] = atoi(p);
	}

    while(p = strtok(NULL,"|") )
    {
        if(gConf.gwAppHeader > 1)
		{
			telcoArray[i++]= (atoi(p)+gConf.gwAddTelco);
			
		}else{
			telcoArray[i++]= atoi(p);
		}
		
        if( i >= 6 )
        {
            break;
        }
    }
    
    //sprintf(szTelco, "%d|%d|%d|%d|%d|%d", 
    //        telcoArray[0], telcoArray[1], telcoArray[2], telcoArray[3], telcoArray[4], telcoArray[5]);

    //real-time queue
    if(strncmp(szQType, "type_real", 9) == 0) 
    {
        return telcoArray[4];
    }
    //batch queue
    else if(strncmp(szQType, "type_bt", 7) == 0)
    {
        return telcoArray[5];
    }
    else
    {
        return telcoArray[4];
    }
}

void writeLogMMSData(CMMSPacketSend& mmsPacketSend,long long mmsid, int ctnid)
{
    logPrintS(1,"[INF] send message mmsid[%lld]ctnid[%d]key[%s]extend[]subject[%s]dst[]call[%s]",
            mmsid,
            ctnid,
            mmsPacketSend.getKeyValue(),
            //mmsPacketSend.getExtendValue(),
            mmsPacketSend.getSubjectValue(),
         /*   mmsPacketSend.getReceiverValue(),*/
            mmsPacketSend.getSenderValue()
            /*mmsPacketSend.getContentCntValue(),
            mmsPacketSend.getPsktynValue(),
            mmsPacketSend.getPktfynValue(),
            mmsPacketSend.getPlgtynValue(),
            mmsPacketSend.getTextCntValue(),
            mmsPacketSend.getImgCntValue(),
            //mmsPacketSend.getAudCntValue(),
            mmsPacketSend.getMpCntValue(),
            mmsPacketSend.getBcastCntValue(),
            mmsPacketSend.getMsgOrgSizeValue()*/
            );
}


SenderProcess::SenderProcess()
{
	bDayWarnCheck=false;
	bMonWarnCheck=false;
}

void SenderProcess::SenderMain(int sockfd,CLogonDbInfo& logonDbInfo)
{
	int ret;
	CLogonUtil util;
	CAdminUtil admin;
	CKSSocket db;
	CProcessInfo processInfo;
	CMonitor monitor;

	memset(&processInfo,0x00,sizeof(processInfo));
	memset(&gSenderInfo,0x00,sizeof(gSenderInfo));
	  
	strcpy(processInfo.processName,logonDbInfo.szSenderName);
	get_timestring("%04d%02d%02d%02d%02d%02d",time(NULL),processInfo.startTime);
	
	sprintf(processInfo.szPid,"%d",getpid());
	strcpy(processInfo.logonDBName,gConf.logonDBName);
	
	util.findValueParse(logonDbInfo.szReserve, "mms_tel", gSenderInfo.szSmsTelcoInfo);
	util.findValueParse(logonDbInfo.szReserve, "mms_yn",  gSenderInfo.szSmsFlag);
	util.findValueParse(logonDbInfo.szReserve, "res_method",  gSenderInfo.szResMethod);
	util.findValueParse(logonDbInfo.szReserve, "timeout",  gSenderInfo.szTimeout);
	util.findValueParse(logonDbInfo.szReserve, "q_type",  gSenderInfo.szQType);
	util.findValueParse(logonDbInfo.szReserve, "block_yn",  gSenderInfo.szBlockYN);
	
	/*
	 * alim talk flag added
	 * sender_key  : member company key
	 * partner_key : hub partner key(KSKYB)
	 */
	util.findValueParse(logonDbInfo.szReserve, "sender_key" ,  gSenderInfo.szSenderKey);
	
	//strcpy(gSenderInfo.szUrlTelcoInfo,"0");
	//strcpy(gSenderInfo.szUrlFlag,"0");
	
	logPrintS(0,"[INF] send process sender main start sockfd[%d]CID[%s]processInfo.startTime[%s]pid[%s]logonDbInfo.Reserve[%s]senderInfo[%s]smsFlag[%s]SenderKey[%s]BlockYN[%s]"
            ,sockfd
            ,logonDbInfo.szCID
            ,processInfo.startTime
            ,processInfo.szPid
            ,logonDbInfo.szReserve
            ,gSenderInfo.szSmsTelcoInfo
            ,gSenderInfo.szSmsFlag
			,gSenderInfo.szSenderKey
			,gSenderInfo.szBlockYN
			);
	
	util.displayLogonDbInfo(logonDbInfo,_MONILOG);

	CKSSocket hRemoteSock;

	int recvLen;
   
	hRemoteSock.attach(sockfd);
   
	ret = admin.createDomainID(logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
	
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] socket_domain create failed - CID[%s]classify[%c]domain_path[%s]", logonDbInfo.szCID, logonDbInfo.classify, gConf.domainPath);
        goto SenderEND;
	}
	
	monitor.Init("logon7", "sender", processInfo.processName, logonDbInfo.szCID, logonDbInfo.nmPID, logonDbInfo.szIP);
	time(&SLastTLink);
	
	nCurAccCnt = 0;

	memset(szLimitTime, 0x00, sizeof(szLimitTime));
	get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), szLimitTime);	// get current date
	memset(szLimitCurTime, 0x00, sizeof(szLimitCurTime));
    
	while(bSActive)
	{
		//logPrintS(1,"[STP] -0- []");
				
		// time measurement log : time before message reception
		/*				
	 	struct tm *d;
		struct timeval val;
		gettimeofday(&val, NULL);
		d=localtime(&val.tv_sec);		
		logPrintS(1,"0[Msg Recv Before]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
		*/					
		
		//logPrintS(1,"0[MB]");
		
		ret = hRemoteSock.recvAllMsg(3);
		
		/*if(strlen(hRemoteSock.getMsg())>0)
		{
			logPrintS(1,"0[MF]");
			
			char socketLog[2000];
			
			strncpy(socketLog,hRemoteSock.getMsg(),sizeof(socketLog)-1);
			
			logPrintS(1,"socketLog[%s]",socketLog);
			
		}*/
		
		//ret = util.recvPacket(hRemoteSock,buff,0,10000);
		if( ret < 0)
		{
			logPrintS(0,"[ERR] socket read msg failed - [%s][%d]",hRemoteSock.getErrMsg(), errno);
			goto SenderEND;
		}
		      
		if( ret == 0 )
		{
			//wait_a_moment(logonDbInfo.nmCNT);
			ret = admin.checkPacket(processInfo,logonDbInfo,sum);// check admin packet
			if( ret < 0 )
			{
				logPrintS(0,"[ERR] socket_domain packet check failed - CID[%s]ErrMsg[%s]",logonDbInfo.szCID,admin.getErrMsg());
				goto SenderEND;
			}
			//logPrintS(1,"[STP] -1- []");
	
			switch(ret) 
			{
				case 3: // end
					bSActive = false;
					
					continue;
				case 5: // info
					memset(gSenderInfo.szSmsTelcoInfo, 0x00, sizeof(gSenderInfo.szSmsTelcoInfo)); 
					memset(gSenderInfo.szSmsFlag, 0x00, sizeof(gSenderInfo.szSmsFlag)); 
					memset(gSenderInfo.szResMethod, 0x00, sizeof(gSenderInfo.szResMethod));
					memset(gSenderInfo.szTimeout, 0x00, sizeof(gSenderInfo.szTimeout));
					memset(gSenderInfo.szQType, 0x00, sizeof(gSenderInfo.szQType));
					memset(gSenderInfo.szBlockYN, 0x00, sizeof(gSenderInfo.szBlockYN));
					util.findValueParse(logonDbInfo.szReserve, "mms_tel" ,gSenderInfo.szSmsTelcoInfo); 
					util.findValueParse(logonDbInfo.szReserve, "mms_yn"	,gSenderInfo.szSmsFlag);
					util.findValueParse(logonDbInfo.szReserve, "res_method",  gSenderInfo.szResMethod);
					util.findValueParse(logonDbInfo.szReserve, "timeout",  gSenderInfo.szTimeout);
					util.findValueParse(logonDbInfo.szReserve, "q_type",  gSenderInfo.szQType);
					util.findValueParse(logonDbInfo.szReserve, "block_yn",  gSenderInfo.szBlockYN);
					nCurAccCnt = 0;
					logPrintS(0,"[INF] info modify ok");
					break;
				default:
					break;
			}
	
			time(&SThisT);	
			ret = (int)difftime(SThisT,monLastT);
			if( ret > 30 )
			{
				monitor.setDataSum(sum);
				monitor.setCurDate();
				monitor.send(gConf.monitorName);
				time(&monLastT);
				sum=0;
			}
			continue; // no data
		}
		 
		//logPrintS(1,"[STP] -3- []");
		
		// write log
		//log commented out.2011.11.23.
#ifdef _DEBUG
	    _monPrint(_MONILOG,(char*)hRemoteSock.getMsg());
	    printf("\n\n%s\n\n", (char*)hRemoteSock.getMsg());
#endif
			
#ifdef TIME
		/* gettimeofday */
		struct timeval timefirst, timesecond;
		struct timezone tzp;
		int secBuf, microsecBuf;
		float timeBuf;
		
		gettimeofday(&timefirst,&tzp);
		/* gettimeofday */
#endif
		//logPrintS(1,"nCurAccCnt(%d)",nCurAccCnt);
		
		ret = classifyS(monitor, processInfo, logonDbInfo, db, hRemoteSock);
		
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] classifyS Error ret [%d]",ret);
			goto SenderEND;
		}
		//logPrintS(1,"[STP] -4- []");
			
#ifdef TIME
		gettimeofday(&timesecond,&tzp);
		secBuf 		= (timesecond.tv_sec - timefirst.tv_sec);
		microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
		timeBuf 	= microsecBuf;
		timeBuf 	= timeBuf / 1000000;
		timeBuf 	= timeBuf + secBuf;
		logPrintS(0,"senderProcess db time [%f]",timeBuf);
#endif
    }

SenderEND:
	logPrintS(0,"[INF] socket END sockfd[%d]CID[%s]", hRemoteSock.getSockfd(), logonDbInfo.szCID);
	hRemoteSock.close();
	
	return;
}


int SenderProcess::classifyS(CMonitor& monitor,CProcessInfo& processInfo,CLogonDbInfo& logonDbInfo,CKSSocket& db,CKSSocket& hRemoteSock) 
{
	int ret = 0;
	int rptRet = 0;
	char szYYYYMM[32];
	string strPacketHeader;
	
	CSenderDbMMSID senderDbMMSID;
	CBcastData bCastData;

	strPacketHeader = "";
	strPacketHeader.reserve(0);
			
	strPacketHeader.insert(0,hRemoteSock.getMsg(),30);
	
	
	if( strstr(strPacketHeader.c_str(),"BEGIN PING\r\n") ) 
	{
		fflush(stdout);
		//logPrintS(0,"[INF] socket link recv");
		logPrintS(0,"[INF] SENDER PING recv");
		
		ret = sendPong(hRemoteSock);
		monitor.setLinkTime();
		if( ret < 0 ) 
		{
			return ret;
		}
	} 
	else if ( strstr(strPacketHeader.c_str(),"BEGIN FTKUPGRADE\r\n") )  
	{
		
		//printf("%s", hRemoteSock.getMsg());
	
		//printf("%s", strPacketHeader.c_str());
  
		/*
		    20140212 : mmsPacketSend.parse fix return 100
			return parse result
				-1 : MData Header info error
				 0 : OK
		*/
		/*
		 * STEP1 : message parsing
		 *   if aes-base64, decrypt in parse()
		 */
		ret = mmsPacketSend.parse((char*)hRemoteSock.getMsg());
		if( ret != 100 ) 
		{
			logPrintS(0,"[ERR] packet parse failed - ErrMsg[%s]",mmsPacketSend.getErrorMsg());
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"Msg Parsing Error");
			//return ret;
			return 0;
		}

		/*
		 * STEP2 : MMS ID lookup
		 */
		ret = getMMSID2DB(senderDbMMSID,szSenderID);
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get MMSID failed cid[%s] ptn_sn[%s]",szSenderID,mmsPacketSend.getKeyValue());
			sendAck(hRemoteSock, mmsPacketSend, ret, senderDbMMSID.ctnid, "DB GET MMS ID FAILED.");
			
			rptRet = setMMSRPTTBL(0,0,mmsPacketSend,8003,"GetMMSIDFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] getMMSID2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s]",szSenderID,mmsPacketSend.getKeyValue());
			}
			//return ret;
			return 0;
		}
	
		/*
		 * STEP3 : CTN ID lookup
		 */
		
		/*ret = getCTNID2DB(senderDbMMSID);
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get CTNID failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB GET CTN ID FAILED.");
			
			rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,8006,"GetCTNIDFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] getCTNID2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
			}
			
			//return ret;
			return 0;
		}*/
		
		
		/*
		 * STEP4 : message log
		 */
		//writeLogMMSData(mmsPacketSend,senderDbMMSID.mmsid,senderDbMMSID.ctnid);// LOG write

		/*
		 * STEP5 : message file creation
		 */

		// FriendTalk Up : no file creation
		/*
		CMMSFileProcess mmsFileProcess;// write file object
 	
		memset(szYYYYMM, 0x00 ,sizeof(szYYYYMM));  //CCL(szYYYYMM);
		get_timestring("%04d%02d",time(NULL),szYYYYMM);
		trim(szYYYYMM,strlen(szYYYYMM));

		ret = mmsFileProcess.writeTalk(db
 				,mmsPacketSend
 				,gConf.ContentPath
 				,senderDbMMSID.mmsid
 				,szYYYYMM
 				,szSenderID
 				,senderDbMMSID.ctnid
 				,gConf.dbRequestTimeOut
 				,senderDbDomainName
 				);

		if(ret < 0 )
		{
			logPrintS(0,"[ERR] file write failed [%d]", ret);

			// ret == -2 : IMG size exceeds 1M error
			if ( ret == -2)
			{
				sendAck(hRemoteSock,mmsPacketSend,2104,senderDbMMSID.ctnid  ,"Content size exceeded");
				
				rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,8007,"OverContentsSize.",szSenderID);
			
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] contents size setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
			}
			else
			{
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid  ,"FILE WRITE FAILED.");
				
				rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,8008,"ContentsFileErr.",szSenderID);
			
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] contents write setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
			}	
			//return ret;
			return 0;
		}
		*/
		
		if(strncmp(gSenderInfo.szBlockYN,"Y",1)==0)
    	{
    		ret =  sendAck(hRemoteSock,mmsPacketSend,8012, senderDbMMSID.ctnid ,"Fail");
		
			if ( ret  < 0 )
			{
				logPrintS(0,"[ERR] socket send ack failed");
			}

			rptRet = setMMSRPTTBL( 0, senderDbMMSID.mmsid, mmsPacketSend, 8012, "SendBlocked.", szSenderID);
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSRPTTBL block cid[%s] ptn_sn[%s] mmsid[%lld]", 
					szSenderID, 
					mmsPacketSend.getKeyValue(), 
					senderDbMMSID.mmsid
					);
			}
			
			return 0;
		}
		else
		{
			// FriendTalkUP image X
			//content table insertion
			/*
			ret = setMMSCTNTBL2DB(mmsFileProcess);
        	if ( ret < 0)
        	{
        	    logPrintS(0,"[ERR] db insert table content failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
        	    sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT CONTENT TABLE FAILED.");
        	    
        	    rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,8009,"InsertCNTTBLFailed.",szSenderID);
				
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] setMMSCTNTBL2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
				
        	    //return ret;
        	    return 0;
        	}
        	*/
		
			/*
			 * STEP6 : SEND TABLE registration
			 */
			ret = setMMSTBL2DB(senderDbMMSID.mmsid, senderDbMMSID.ctnid,logonDbInfo.nmPRT);  // send table
			if ( ret < 0 )
			{
				logPrintS(0,"[ERR] db insert table MMSTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT MMSTBL FAILED.");
				
				rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,8004,"InsertMMSTBLFailed.",szSenderID);
				
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] setMMSTBL2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
				
				return 0;
			}
		}
	
		/*
		 * STEP7 : AQUEUE transmission
		 */
		//ret = setMMSMSG2DB_TALK(senderDbMMSID.mmsid, mmsPacketSend, logonDbInfo.nmPRT);
		//ret = setMMSMSG2DB_FTK(senderDbMMSID.mmsid, mmsPacketSend, logonDbInfo.nmPRT);
		ret = setMMSMSG2DB_FTKUP(senderDbMMSID.mmsid, mmsPacketSend, logonDbInfo.nmPRT);
		
		if ( ret < 0 )
		{
			logPrintS(0,"[ERR] db insert MMS MSG failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT MMS MSG FAILED.");
			
			rptRet = setMMSRPTTBL(1,senderDbMMSID.mmsid,mmsPacketSend,8005,"InsertMMSMSGFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSMSG2DB_ATK setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
			}
			
			return 0;
		}
				
		/*
		 * STEP8 : CLIENT ACK message transmission
		 */
		ret = sendAck(hRemoteSock,mmsPacketSend,ret, senderDbMMSID.ctnid ,"Succ");
		if ( ret  < 0 )
		{
			logPrintS(0,"[ERR] socket send ack failed");
		}
		logPrintS(1,"[%lld] : Send Ack End",senderDbMMSID.mmsid);
		
		monitor.setDataTime();    
		nCurAccCnt++;
        
		/*
		 * STEP9 : transmission count limit check
		 */
		ret =	SenderLimit(logonDbInfo);
		
		if(ret == -1)
		{
			logPrintS(0,"[ERR] limit SendreLimit ret [%d]",ret);
			return -1;
		}		//	logPrintS(1,"ret(%d)nCurAccCnt(%d)",ret,nCurAccCnt);
		//logPrintS(1,"[DBG] -6- [%lld]",senderDbMMSID.mmsid);

	}
	// FriendTalk UP image upload X
#ifdef FTK_IMG
	else if (strstr(strPacketHeader.c_str(), "BEGIN FTKIMGURL\r\n"))
	{
		ST_IMG_RES res;

		/* 1. parsing telegram */
		ret = mmsPacketSend.parse((char *) hRemoteSock.getMsg());
		if (100 != ret) {
			logPrintS(0, "[ERR] packet parse failed - ErrMsg[%s]",
					mmsPacketSend.getErrorMsg());
			sendAck(hRemoteSock, mmsPacketSend, ret, senderDbMMSID.ctnid,
					"message parsing error");
			return 0;
		}

		logPrintS(1, "[INF] kakao image url request; key: %s", mmsPacketSend.getKeyValue());

		ret = getCTNID2DB(senderDbMMSID);
		if(ret < 0)
		{
			logPrintS(0, "[ERR] db select get CTNID failed cid[%s] ptn_sn[%s] mmsid[%lld]",
			          szSenderID, mmsPacketSend.getKeyValue(), senderDbMMSID.mmsid);
			sendAck(hRemoteSock, mmsPacketSend, ret, senderDbMMSID.ctnid, "DB GET CTN ID FAILED.");
			
			rptRet = setMMSRPTTBL(0, senderDbMMSID.mmsid, mmsPacketSend, 8006, "GetCTNIDFailed.", szSenderID);
			if(rptRet < 0)
			{
				logPrintS(0, "[ERR] getCTNID2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",
				          szSenderID, mmsPacketSend.getKeyValue(), senderDbMMSID.mmsid);
			}
			//return ret;
			return 0;
		}	

		logPrintS(1, "[INF] get content ID; key: %s, ctn_id: %d", mmsPacketSend.getKeyValue(), senderDbMMSID.ctnid);

		/* 2. save image file */
		memset(szYYYYMM, 0x00, sizeof(szYYYYMM)); //CCL(szYYYYMM);
		get_timestring("%04d%02d", time(NULL), szYYYYMM);
		trim(szYYYYMM, strlen(szYYYYMM));

		CMMSFileProcess mmsFileProcess; // write file object
		ret = mmsFileProcess.writeTalk(db, mmsPacketSend, gConf.ContentPath,
				senderDbMMSID.mmsid, szYYYYMM, szSenderID, senderDbMMSID.ctnid,
				gConf.dbRequestTimeOut, senderDbDomainName);
		if (0 > ret) {
			logPrintS(0, "[ERR] file write failed [%d]", ret);

			if (-2 == ret) {
				/* Image size is over 1 Mi */
				sendAck(hRemoteSock, mmsPacketSend, 2104, senderDbMMSID.ctnid,
						"image size is over 1Mi");

				rptRet = setMMSRPTTBL(0, senderDbMMSID.mmsid, mmsPacketSend,
						8007, "OverContentsSize.", szSenderID);
				if (0 > rptRet) {
					logPrintS(0, "[ERR] contents size setMMSRPTTBL failed "
							"cid[%s] ptn_sn[%s] mmsid[%lld]", szSenderID,
							mmsPacketSend.getKeyValue(), senderDbMMSID.mmsid);
				}
			} else {
				/* Image file is damaged */
				sendAck(hRemoteSock, mmsPacketSend, ret, senderDbMMSID.ctnid,
						"FILE WRITE FAILED.");

				rptRet = setMMSRPTTBL(0, senderDbMMSID.mmsid, mmsPacketSend,
						8008, "ContentsFileErr.", szSenderID);
				if (0 > rptRet) {
					logPrintS(0, "[ERR] contents write setMMSRPTTBL failed"
							" cid[%s] ptn_sn[%s] mmsid[%lld]", szSenderID,
							mmsPacketSend.getKeyValue(), senderDbMMSID.mmsid);
				}
			}
			return 0;
		}

		// result: mmsPacketSend.setImgPath(imgPath);
		/* 3. curl command: send image to kakao server */
		CCurl curl;
		curl.init();
		curl.setHeaderPost("Content-type: multipart/form-data");
		curl.setHeaderPost("Expect:");
		curl.setHeaderPost("account: <EMAIL>");

		curl.makeMultiFormData("image", mmsPacketSend.getImgPathValue());

		if (0 == strcmp(mmsPacketSend.getWideValue(), "Y")) {
			/* wide */
			curl.setOptHttpPost(gConf.wide_img_target_url);
		} else {
			/* general */
			curl.setOptHttpPost(gConf.img_target_url);
		}

		logPrintS(1, "[INF] send image to kakao; key: %s, wide: %s, image path:%s", 
			mmsPacketSend.getKeyValue(), mmsPacketSend.getWideValue(), mmsPacketSend.getImgPathValue());

		CURLcode rVal = curl.perform();
		
		long httpcode = curl.getHttpCode();
		logPrintS(1, "[DEB] http code from cURL, %ld", httpcode, rVal);

		if (CURLE_OK == rVal) {
			/* curl success */
			logPrintS(1, "[INF] success to curl request; key: %s, curl code: %d", 
				mmsPacketSend.getKeyValue(), rVal);
			/* 4. parsing result(JSON format) */
			Json::Value root;
			Json::Reader reader;

			bool parsingSuccessful = reader.parse(curl.response.c_str(), root);
			if (!parsingSuccessful) {
				logPrintS(0, "[ERR] Failed to parse response msg");
				return -1;
			}
			//res.received_at = root.get("received_at", "").asString();
			res.code = root.get("code", "").asString();
			res.message = root.get("message", "").asString();

			if (res.code != "0000") {
				res.img_url = root.get("image", "").asString();
			}

			logPrintS(1, "[INF] image url results; code: %s, message: %s", res.code.c_str(), res.message.c_str());

		} else {
			/* curl error */
			logPrintS(0, "[ERR] failed to curl request; %s, %d", curl_easy_strerror(rVal), rVal);
		}
		
		curl.cleanAll();
		
		/* 5. ack */
		ret = send_image_url_ack(hRemoteSock, mmsPacketSend,
				res.code, res.message, res.img_url);
		if (0 > ret) {
			/* socket error  */
		}
	}	 
#endif
	else  // error
	{
		logPrintS(0,"[INF] invalid msg header data - [%s] ", strPacketHeader.c_str());

		fflush(stdout);
		ret = -1;
	}

	return ret;
}								


int SenderProcess::SenderLimit(CLogonDbInfo& logonDbInfo)
{
	int ret;
	char szTemp[256];
	
	//* < brief sending limit check
	if (atoi(logonDbInfo.szLimitType) != 0)
	{
		memset(szTemp	,0x00	,sizeof(szTemp));
	
		get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), szLimitCurTime);	// get current date
		
		if (strcmp(szLimitTime,"") == 0)
		{
			strcpy(szLimitTime,szLimitCurTime);	// get year-month-day value
		}

		ret = LimitCheck(logonDbInfo);
		//perform service restriction and notification based on count calculation
		// logPrintS(1,"ret(%d)",ret);
		switch (ret)
		{
			case 9 : // reset accumulated count and execute procedure due to day change
			case 10 : // reset accumulated count and execute procedure due to month change
				if (ret == 9)
					logPrintS(1,"[INF]day change total count reset and run");
				if (ret == 10)
					logPrintS(1,"[INF]month change total count reset and run");
	
				//initialize sending limit variables
				bDayWarnCheck = false;
				bMonWarnCheck = false;
				
				if (ret == 9)
					logonDbInfo.nMonAccCnt += logonDbInfo.nCurAccCnt;	//accumulate monthly count on day change
				if (ret == 10)
					logonDbInfo.nMonAccCnt = 0;	//reset monthly count on month change
					
				logonDbInfo.nDayAccCnt = 0;	//always reset daily count on day/month change
				nCurAccCnt = 0;	//always reset current accumulated count on day/month change
				memset(szLimitTime,(char)NULL,sizeof(char)*16);
				break;
			default :
				break;
		}
				
		switch (ret)
		{
			case 0 : // no change
				break;
			case 1 : // daily service restriction
				logPrintS(1,"[INF] daily limit [%d]"	,logonDbInfo.nDayLimitCnt);
				return -1;
			case 2 : // monthly service restriction
				logPrintS(1,"[INF] monthly limit [%d]"	,logonDbInfo.nMonLimitCnt);
				return -1;
			case 3 : // daily service restriction + alarm
				sprintf(szTemp,"[ERR] daily limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(0,"[%s]"	,szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				return -1;
			case 4 : // monthly service restriction + alarm
				sprintf(szTemp,"[ERR] monthly limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(0,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				return -1;
			case 5 : // daily alarm
				sprintf(szTemp,"[INF] daily limit orver - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(1,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				break;
			case 6 : // monthly alarm
				sprintf(szTemp,"[INF] monthly limit over - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(1,"%s",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				break;
			case 7 : // daily threshold alarm
				if (!bDayWarnCheck)
				{
					bDayWarnCheck = true;
					sprintf(szTemp,"[INF] daily limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nDayWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin daily limit msg send failed",0,0);
					}
						
				}
				break;
			case 8 : // monthly threshold alarm
				if (!bMonWarnCheck)
				{
					bMonWarnCheck = true;
					sprintf(szTemp,"[INF] monthly limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nMonWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin monthly limit msg send failed",0,0);
					}
						
				}
				break;
			default :
				break;
		}
	}
	
	return 0;
}


//* < brief sending limit check
int SenderProcess::LimitCheck(CLogonDbInfo& logonDbInfo)
{
/* return value
	1 : daily service restriction
	2 : monthly service restriction
	3 : daily service restriction + alarm
	4 : monthly service restriction + alarm
	5 : daily alarm
	6 : monthly alarm
	7 : daily threshold alarm
	8 : monthly threshold alarm
	9 : reset accumulated count due to day change
	10 : reset accumulated count due to month change
	0 : no change
*/
	bool bDay=false;
	bool bMon=false;
	int  nDayAccCnt 		= logonDbInfo.nDayAccCnt;
	int  nMonAccCnt 		= logonDbInfo.nMonAccCnt;
	int  nDayWarnCnt 		= logonDbInfo.nDayWarnCnt;
	int  nMonWarnCnt 		= logonDbInfo.nMonWarnCnt;
	int  nDayLimitCnt 		= logonDbInfo.nDayLimitCnt;
	int  nMonLimitCnt 		= logonDbInfo.nMonLimitCnt;
	int  nLimitType 		= atoi(logonDbInfo.szLimitType);
	int  nLimitFlag 		= atoi(logonDbInfo.szLimitFlag);

	//logPrintS(1,"szLimitCurTime(%s)szLimitTime(%s)",szLimitCurTime,szLimitTime);
	if (strncmp(szLimitTime	,szLimitCurTime	,8) != 0)
		bDay = true;	//daily unit
		
	if (strncmp(szLimitTime,szLimitCurTime,6) != 0) 
		bMon = true;	//monthly unit

	if (bDay)
	{
		if (bMon)
		{
			return 10;	// when month change occurred
		}
		else
		{
			return 9;	// when day change occurred
		}
	}
 //logPrintS(1,"nLimitType(%d),nDayWarnCnt(%d),nCurAccCnt(%d),nDayAccCnt(%d)",nLimitType, nDayWarnCnt, nCurAccCnt,nDayAccCnt);
	//service restriction check
	switch (nLimitType)
	{
		case 0:	//no sending restriction applied
			return 0;
		case 1:	//daily,monthly sending restriction
		case 2:	//daily sending restriction
		case 3:	// monthly sending restriction
			if (nLimitType == 1 || nLimitType == 2)
			{
				//daily threshold check (between threshold and limit count)
				if (((nDayAccCnt + nCurAccCnt) > nDayWarnCnt) && ((nDayAccCnt + nCurAccCnt) < nDayLimitCnt))
				{
					logPrintS(1,"[INF] daily limit - limit over [%d/%d]", nDayWarnCnt, (nDayAccCnt + nCurAccCnt)-nDayWarnCnt);
					return 7;
				}
				//daily service restriction check
				if ((nDayAccCnt + nCurAccCnt) > nDayLimitCnt)
				{
					logPrintS(1,"[INF] daily limit - config value [%d]", nDayLimitCnt);
				
					switch (nLimitFlag)
					{
						case 1 :
							return 1;
						case 2 :
							return 3;
						case 3 :
							return 5;
						default :
							return 0;
					}
				}
			}
//logPrintS(1,"nMonWarnCnt(%d),nMonAccCnt(%d),nMonLimitCnt(%d)",nMonWarnCnt, nMonAccCnt+nCurAccCnt+nDayAccCnt, nMonLimitCnt);

			if (nLimitType == 1 || nLimitType == 3)
			{
				//monthly threshold check (between threshold and limit count)
				//if (((nMonAccCnt + nCurAccCnt) > nMonWarnCnt) && ((nMonAccCnt + nCurAccCnt) < nMonLimitCnt))
				if (((nMonAccCnt + nCurAccCnt + nDayAccCnt) > nMonWarnCnt) && ((nMonAccCnt + nCurAccCnt + nDayAccCnt) < nMonLimitCnt))
				{
					logPrintS(1,"[INF] monthly limit - limit over [%d/%d]", nMonWarnCnt, (nMonAccCnt + nCurAccCnt + nDayAccCnt)-nMonWarnCnt);
					return 8;
				}
				//monthly service restriction check
				//	if ((nMonAccCnt + nCurAccCnt) > nMonLimitCnt)
				if ((nMonAccCnt + nCurAccCnt + nDayAccCnt) > nMonLimitCnt)
				{
					logPrintS(1,"[INF] monthly limit - config value [%d]", nMonLimitCnt);
					switch (nLimitFlag)
					{
						case 1 :
							return 2;
						case 2 :
							return 4;
						case 3 :
							return 6;
						default :
							return 0;
					}
				}
			}
		default:
			return 0;
		break;
	}
	return 0;
}

int setMMSRPTTBL(int type, long long nMMSId, CMMSPacketSend& mmsPacketSend, 
		int nResCode,char* res_text,char* cid)
{
	int ret;
	CSenderDbMMSRPTQUE senderDbMMSRPTQUE;
	/*CMData mData;
	ret = mmsPacketSend.getMDataFirst(mData);
	if( ret != 0 )
	{
		logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}*/

	memset(&senderDbMMSRPTQUE,0x00,sizeof(senderDbMMSRPTQUE));

	/*if(strncmp(mData.strEncoding.c_str(), " aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0)
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key();
		//logPrintS(1,"receiverNum:%d", atoi(mmsPacketSend.getPhoneSizeValue()));
		en.decrypt(receiverNum, receiverNum, atoi(mmsPacketSend.getPhoneSizeValue()));
		strcpy(senderDbMMSRPTQUE.szDstAddr, (char*)receiverNum);
		
		free(receiverNum);
	}
	else
	{
		strcpy(senderDbMMSRPTQUE.szDstAddr , mmsPacketSend.getReceiverValue());
	}*/
	
	strcpy(senderDbMMSRPTQUE.szDstAddr , "010");
	
	strcpy(senderDbMMSRPTQUE.szPtnSn   , mmsPacketSend.getKeyValue() );
	senderDbMMSRPTQUE.nMMSId 		= nMMSId;
	senderDbMMSRPTQUE.res_code     = nResCode;
	strcpy(senderDbMMSRPTQUE.res_text   , res_text);
	
	senderDbMMSRPTQUE.nTelcoId = getTelcoId(
		mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, gSenderInfo.szQType);
	
	senderDbMMSRPTQUE.nType 		= type;
	memcpy(senderDbMMSRPTQUE.szCid, cid, 10);	
	
	ret = g_oracle.setSendReportData(senderDbMMSRPTQUE);
	
	if (ret < 0)
	{
		if(type == 1)
		{
			logPrintS(0,"[ERR] setSendReportData TBL MMSID[%lld] PtnSn[%s] ret[%d]",
				nMMSId, senderDbMMSRPTQUE.szPtnSn  , ret);
		}
		else
		{
			logPrintS(0,"[ERR] setSendReportData QUEUE MMSID[%lld] PtnSn[%s] ret[%d]",
				nMMSId, senderDbMMSRPTQUE.szPtnSn  , ret);	
		}
	}
	
	return ret;
}

